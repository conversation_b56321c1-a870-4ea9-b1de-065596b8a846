"""
API routes for the Live Email Classifier application
"""

from flask import Blueprint, request, jsonify, session
import logging

api_bp = Blueprint('api', __name__, url_prefix='/api')
logger = logging.getLogger(__name__)

@api_bp.route('/analyze', methods=['POST'])
def analyze_email():
    """Analyze a single email."""
    try:
        from app.services.email_analyzer import EmailAnalyzer
        
        data = request.get_json()
        email_text = data.get('email_text', '')

        if not email_text:
            return jsonify({'error': 'No email text provided'}), 400

        # Create email data
        email_data = {
            'subject': data.get('subject', ''),
            'body': email_text,
            'sender': data.get('sender', '')
        }

        # Initialize email analyzer
        email_analyzer = EmailAnalyzer()
        
        # Analyze the email
        analysis = email_analyzer.analyze_email(email_data)

        # Return the analysis
        return jsonify({
            'category': analysis['category'],
            'confidence': analysis['confidence'],
            'keywords': analysis['keywords'],
            'probabilities': analysis.get('probabilities', {})
        })

    except Exception as e:
        logger.error(f"Error analyzing email: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/stats')
def get_stats():
    """Get statistics for the dashboard."""
    try:
        from app.services.database import get_statistics
        
        # Get user ID from session
        user_id = session.get('user_id')

        # Get statistics from database
        stats = get_statistics(user_id)

        # Format statistics for frontend
        formatted_stats = {
            'totalEmails': stats.get('total_emails', 0),
            'classifiedEmails': stats.get('classified_emails', 0),
            'spamEmails': stats.get('spam_emails', 0),
            'hrEmails': stats.get('hr_emails', 0),
            'supportEmails': stats.get('support_emails', 0),
            'casualEmails': stats.get('casual_emails', 0),
            'accuracy': stats.get('accuracy', 0) * 100  # Convert to percentage
        }

        return jsonify(formatted_stats)
    except Exception as e:
        logger.error(f"Error getting statistics: {str(e)}")

        # Return default statistics if there's an error
        default_stats = {
            'totalEmails': 0,
            'classifiedEmails': 0,
            'spamEmails': 0,
            'hrEmails': 0,
            'supportEmails': 0,
            'casualEmails': 0,
            'accuracy': 0
        }

        return jsonify(default_stats)

@api_bp.route('/emails')
def get_email_list():
    """Get list of emails from Gmail and analyze them."""
    try:
        from app.services.google_auth import get_gmail_service, fetch_emails
        from app.services.email_analyzer import EmailAnalyzer
        from app.services.database import save_email, get_emails, update_statistics
        
        # Check if in demo mode
        if 'demo_mode' in session and session['demo_mode']:
            # Return demo data
            demo_emails = [
                {
                    'id': 'email-1',
                    'subject': 'URGENT: Your Account Has Been Compromised',
                    'sender': '<EMAIL>',
                    'date': '2023-05-15 14:32',
                    'snippet': 'We have detected suspicious activity on your account. Your account access will be terminated unless you verify...',
                    'category': 'spam',
                    'confidence': 0.92,
                    'keywords': ['urgent', 'account', 'compromised', 'verify', 'suspicious']
                },
                {
                    'id': 'email-2',
                    'subject': 'Quarterly Performance Review Scheduled',
                    'sender': '<EMAIL>',
                    'date': '2023-05-14 09:15',
                    'snippet': 'This is a reminder that your quarterly performance review has been scheduled for next week. Please prepare...',
                    'category': 'professional',
                    'confidence': 0.88,
                    'keywords': ['quarterly', 'performance', 'review', 'scheduled', 'prepare']
                },
                {
                    'id': 'email-3',
                    'subject': 'Re: Ticket #45678 - Login Issue',
                    'sender': '<EMAIL>',
                    'date': '2023-05-13 16:45',
                    'snippet': 'Thank you for contacting technical support regarding your login issue. Based on our investigation...',
                    'category': 'professional',
                    'confidence': 0.95,
                    'keywords': ['ticket', 'login', 'issue', 'support', 'technical']
                },
                {
                    'id': 'email-4',
                    'subject': 'Weekend plans?',
                    'sender': '<EMAIL>',
                    'date': '2023-05-12 18:22',
                    'snippet': 'Hey, what are you up to this weekend? Want to grab lunch on Saturday?',
                    'category': 'casual',
                    'confidence': 0.91,
                    'keywords': ['weekend', 'plans', 'lunch', 'saturday', 'grab']
                },
                {
                    'id': 'email-5',
                    'subject': 'LAST CHANCE: 80% Off Everything!',
                    'sender': '<EMAIL>',
                    'date': '2023-05-11 10:05',
                    'snippet': 'Don\'t miss out on our biggest sale of the year! 80% off everything store-wide. Sale ends tonight!',
                    'category': 'spam',
                    'confidence': 0.89,
                    'keywords': ['chance', 'sale', 'discount', 'offer', 'limited']
                }
            ]

            # Save demo emails to database if user is logged in
            if 'user_id' in session:
                try:
                    user_id = session['user_id']
                    for email in demo_emails:
                        save_email(email, user_id)

                    # Update statistics
                    update_statistics(user_id)
                except Exception as db_error:
                    logger.error(f"Error saving demo emails to database: {str(db_error)}")

            return jsonify(demo_emails)

        # Check if authenticated
        gmail_service = None
        if 'credentials' in session:
            gmail_service, _ = get_gmail_service()
        else:
            # Return empty list if not authenticated
            return jsonify([])

        # Get query parameters
        max_results = request.args.get('max_results', 10, type=int)
        query = request.args.get('query', '')

        # Check if we should use database or fetch from Gmail
        use_database = request.args.get('use_database', 'true').lower() == 'true'

        if use_database and 'user_id' in session:
            # Get emails from database
            db_emails = get_emails(
                user_id=session['user_id'],
                limit=max_results,
                category=request.args.get('category')
            )
            return jsonify(db_emails)

        # Fetch emails from Gmail
        emails = fetch_emails(gmail_service, max_results=max_results, query=query)

        if not emails:
            return jsonify([])

        # Initialize email analyzer
        email_analyzer = EmailAnalyzer()

        # Analyze emails
        analyzed_emails = []
        for email in emails:
            analysis = email_analyzer.analyze_email(email)

            analyzed_email = {
                'id': email['id'],
                'thread_id': email.get('thread_id', ''),
                'subject': email['subject'],
                'sender': email['sender'],
                'recipient': email.get('recipient', ''),
                'date': email['date'],
                'snippet': email.get('snippet', ''),
                'body': email.get('body', ''),
                'category': analysis['category'],
                'confidence': analysis['confidence'],
                'keywords': analysis['keywords'],
                'important': email.get('important', False),
                'unread': email.get('unread', False),
                'gmail_category': email.get('category', 'NONE')
            }

            analyzed_emails.append(analyzed_email)

            # Save to database if user is logged in
            if 'user_id' in session:
                try:
                    save_email(analyzed_email, session['user_id'])
                except Exception as db_error:
                    logger.error(f"Error saving email to database: {str(db_error)}")

        # Update statistics
        if 'user_id' in session:
            try:
                update_statistics(session['user_id'])
            except Exception as stats_error:
                logger.error(f"Error updating statistics: {str(stats_error)}")

        return jsonify(analyzed_emails)

    except Exception as e:
        logger.error(f"Error fetching emails: {str(e)}")
        return jsonify({'error': str(e)}), 500
