"""
Live Email Classifier - Main Application
A Flask web application that classifies emails into categories (Spam, Professional, Casual)
using machine learning with a cyberpunk-themed UI.
"""

import os
import json
import logging
import secrets
import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, session
from config import get_config, update_config, save_settings_to_json
from email_processor import EmailProcessor
from classifier import EmailClassifier, EmailPreprocessor, train_model
from google_auth import get_gmail_service, start_oauth_flow, complete_oauth_flow, fetch_emails, authenticate_with_credentials
from email_analyzer import EmailAnalyzer
from database import get_user_by_email, create_user, update_user, save_email, get_emails, update_statistics, get_statistics

# Initialize Flask app
app = Flask(__name__,
            static_folder='static',
            template_folder='templates')

# Configure app
app.secret_key = secrets.token_hex(16)
app.config['SESSION_TYPE'] = 'filesystem'
app.config['OAUTH_REDIRECT_URI'] = 'http://localhost:8080/oauth2callback'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('email_classifier.log')
    ]
)

logger = logging.getLogger(__name__)

# Load configuration
config = get_config()

# Initialize classifier and preprocessor
classifier = None
preprocessor = None

# Initialize email analyzer
email_analyzer = EmailAnalyzer()

# Gmail service
gmail_service = None
user_info = None

def initialize_classifier():
    """Initialize the email classifier."""
    global classifier, preprocessor

    try:
        # Initialize classifier
        classifier = EmailClassifier(
            model_path=config['classifier']['model_path'],
            vectorizer_path=config['classifier']['vectorizer_path'],
            categories=config['classifier']['categories']
        )

        # Train model if it doesn't exist
        if not os.path.exists(config['classifier']['model_path']):
            logger.info("Training new model...")

            # Create sample training data if it doesn't exist
            if not os.path.exists(config['classifier']['training_data']):
                from main import create_sample_training_data
                create_sample_training_data()

            train_result = train_model(
                training_data_path=config['classifier']['training_data'],
                model_path=config['classifier']['model_path'],
                vectorizer_path=config['classifier']['vectorizer_path'],
                categories=config['classifier']['categories']
            )

            if not train_result['success']:
                logger.error(f"Model training failed: {train_result.get('error', 'Unknown error')}")
                return False

        # Load the model
        if not classifier.load_model():
            logger.error("Failed to load model")
            return False

        # Initialize preprocessor
        preprocessor = EmailPreprocessor()

        logger.info("Classifier initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Error initializing classifier: {str(e)}")
        return False

# Routes
@app.route('/')
def index():
    """Render the hacker-themed dashboard page."""
    global gmail_service, user_info

    # Check if already authenticated with Gmail
    if 'credentials' in session:
        if gmail_service is None:
            gmail_service, user_info = get_gmail_service()

    return render_template('hacker_demo.html', user_info=user_info)

@app.route('/auth')
def auth_options():
    """Show authentication options."""
    return render_template('gmail_login.html')

@app.route('/login')
def login():
    """Start the Google OAuth flow."""
    # Generate the authorization URL
    authorization_url, state = start_oauth_flow(app.config['OAUTH_REDIRECT_URI'])

    # Store the state in the session
    session['oauth_state'] = state

    # Redirect to the authorization URL
    return redirect(authorization_url)

@app.route('/gmail-auth', methods=['GET', 'POST'])
def gmail_auth():
    """Authenticate with Gmail using email and password."""
    global gmail_service, user_info

    if request.method == 'GET':
        return render_template('gmail_login.html')

    # Get form data
    email = request.form.get('email')
    password = request.form.get('password')

    # Authenticate with Gmail
    success, message = authenticate_with_credentials(email, password)

    if success:
        # Set user info
        user_info = {
            'email': email,
            'messages_total': 1247,  # Placeholder
            'threads_total': 356     # Placeholder
        }

        # Store credentials in session
        session['credentials'] = True

        # Check if we're in demo mode
        if 'Using demo mode' in message:
            session['demo_mode'] = True

            # Show a flash message
            flash_message = f"""
            <div class="demo-notice">
                <h3>⚠️ Demo Mode Active</h3>
                <p>{message}</p>
                <p>For security reasons, Google requires OAuth2 for Gmail access.</p>
                <p>To use real Gmail integration:</p>
                <ol>
                    <li>Create a project in the <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
                    <li>Enable the Gmail API</li>
                    <li>Create OAuth 2.0 credentials</li>
                    <li>Download as client_secret.json and place in the project root</li>
                </ol>
            </div>
            """

            # Redirect to the dashboard
            return render_template('hacker_demo.html',
                                user_info=user_info,
                                demo_mode=True,
                                flash_message=flash_message)

        # Redirect to the dashboard
        return redirect('/')
    else:
        # Show error message
        return render_template('gmail_login.html', error=message)

@app.route('/demo-auth')
def demo_auth():
    """Demo authentication for when OAuth credentials are not available."""
    # Create a demo user
    global gmail_service, user_info

    # Set demo user info
    user_info = {
        'email': '<EMAIL>',
        'messages_total': 1247,
        'threads_total': 356
    }

    # Store credentials in session
    session['credentials'] = True
    session['demo_mode'] = True

    # Show a flash message
    flash_message = """
    <div class="demo-notice">
        <h3>⚠️ Demo Mode Active</h3>
        <p>Using demo authentication because no valid Google OAuth credentials were found.</p>
        <p>To use real Gmail integration:</p>
        <ol>
            <li>Create a project in the <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
            <li>Enable the Gmail API</li>
            <li>Create OAuth 2.0 credentials</li>
            <li>Download as client_secret.json and place in the project root</li>
        </ol>
    </div>
    """

    # Redirect to the dashboard
    return render_template('hacker_demo.html',
                          user_info=user_info,
                          demo_mode=True,
                          flash_message=flash_message)

@app.route('/oauth2callback')
def oauth2callback():
    """Handle the OAuth callback from Google."""
    global gmail_service, user_info

    # Check if state matches
    if 'oauth_state' not in session:
        flash_message = """
        <div class="error-notice">
            <h3>⚠️ Authentication Error</h3>
            <p>Invalid state parameter. This could be due to an expired session or a security issue.</p>
            <p>Please try authenticating again.</p>
        </div>
        """
        return render_template('hacker_demo.html', flash_message=flash_message)

    state = session['oauth_state']

    # Log the callback URL for debugging
    logger.info(f"OAuth callback received: {request.url}")

    # Complete the OAuth flow
    success, oauth_user_info = complete_oauth_flow(
        app.config['OAUTH_REDIRECT_URI'],
        state,
        request.url
    )

    if not success:
        flash_message = """
        <div class="error-notice">
            <h3>⚠️ Authentication Error</h3>
            <p>Failed to complete the authentication process with Google.</p>
            <p>Please try again or contact support if the issue persists.</p>
        </div>
        """
        logger.error("OAuth flow completion failed")
        return render_template('hacker_demo.html', flash_message=flash_message)

    # Store credentials and user info in session
    session['credentials'] = True
    session['demo_mode'] = False

    # Handle the case where oauth_user_info might be None
    if oauth_user_info is None:
        oauth_user_info = {
            'email': '<EMAIL>',
            'messages_total': 0,
            'threads_total': 0
        }
        logger.warning("OAuth user info is None, using default values")

    email_address = oauth_user_info.get('email', '<EMAIL>')
    session['user_email'] = email_address

    # Set user info
    user_info = oauth_user_info
    logger.info(f"User authenticated: {email_address}")

    # Get the Gmail service
    gmail_service, _ = get_gmail_service()

    # Store user in database
    try:
        # Check if user exists
        db_user = get_user_by_email(email_address)

        # Prepare user data
        user_data = {
            'email': email_address,
            'name': email_address.split('@')[0],  # Simple name extraction
            'profile_picture': '',
            'access_token': session.get('access_token', ''),
            'refresh_token': session.get('refresh_token', ''),
            'token_expiry': datetime.datetime.now() + datetime.timedelta(hours=1)
        }

        if db_user:
            # Update existing user
            update_user(db_user['id'], user_data)
            session['user_id'] = db_user['id']
            logger.info(f"Updated user in database: {email_address}")
        else:
            # Create new user
            user_id = create_user(user_data)
            session['user_id'] = user_id
            logger.info(f"Created new user in database: {email_address}")

        # Update statistics
        user_id = session.get('user_id')
        if user_id is not None:
            update_statistics(user_id)

    except Exception as e:
        logger.error(f"Error storing user in database: {str(e)}")

    # Show success message
    email_address = user_info.get('email', 'your Gmail account')
    flash_message = f"""
    <div class="success-notice">
        <h3>✅ Authentication Successful</h3>
        <p>Successfully connected to Gmail as {email_address}</p>
        <p>You can now analyze your emails using the classifier.</p>
    </div>
    """

    # Redirect to the dashboard
    return render_template('hacker_demo.html',
                          user_info=user_info,
                          flash_message=flash_message)

@app.route('/classify', methods=['GET', 'POST'])
def classify():
    """Render the classify page or process classification request."""
    if request.method == 'GET':
        return render_template('classify.html')

    # Process classification request
    try:
        data = request.get_json()
        email_text = data.get('email_text', '')

        if not email_text:
            return jsonify({'error': 'No email text provided'}), 400

        # Use the email analyzer instead of the old classifier
        email_data = {
            'subject': data.get('subject', ''),
            'body': email_text,
            'sender': data.get('sender', '')
        }

        # Analyze the email
        analysis = email_analyzer.analyze_email(email_data)

        result = {
            'category': analysis['category'],
            'confidence': analysis['confidence'],
            'keywords': analysis['keywords']
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error classifying email: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze', methods=['POST'])
def analyze_email():
    """Analyze a single email."""
    try:
        data = request.get_json()
        email_text = data.get('email_text', '')

        if not email_text:
            return jsonify({'error': 'No email text provided'}), 400

        # Create email data
        email_data = {
            'subject': data.get('subject', ''),
            'body': email_text,
            'sender': data.get('sender', '')
        }

        # Analyze the email
        analysis = email_analyzer.analyze_email(email_data)

        # Return the analysis
        return jsonify({
            'category': analysis['category'],
            'confidence': analysis['confidence'],
            'keywords': analysis['keywords'],
            'probabilities': analysis.get('probabilities', {})
        })

    except Exception as e:
        logger.error(f"Error analyzing email: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/emails')
def emails():
    """Render the emails page."""
    return render_template('emails.html')

@app.route('/settings', methods=['GET', 'POST'])
def settings():
    """Render the settings page or process settings update."""
    if request.method == 'GET':
        return render_template('settings.html', config=config)

    # Process settings update
    try:
        data = request.get_json()

        # Update configuration
        for section, values in data.items():
            for key, value in values.items():
                update_config(section, key, value)

        # Save settings to JSON file
        save_settings_to_json()

        return jsonify({'success': True})

    except Exception as e:
        logger.error(f"Error updating settings: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def get_stats():
    """Get statistics for the dashboard."""
    try:
        # Get user ID from session
        user_id = session.get('user_id')

        # Get statistics from database
        stats = get_statistics(user_id) # type: ignore

        # Format statistics for frontend
        formatted_stats = {
            'totalEmails': stats.get('total_emails', 0),
            'classifiedEmails': stats.get('classified_emails', 0),
            'spamEmails': stats.get('spam_emails', 0),
            'hrEmails': stats.get('hr_emails', 0),
            'supportEmails': stats.get('support_emails', 0),
            'casualEmails': stats.get('casual_emails', 0),
            'accuracy': stats.get('accuracy', 0) * 100  # Convert to percentage
        }

        return jsonify(formatted_stats)
    except Exception as e:
        logger.error(f"Error getting statistics: {str(e)}")

        # Return default statistics if there's an error
        default_stats = {
            'totalEmails': 0,
            'classifiedEmails': 0,
            'spamEmails': 0,
            'hrEmails': 0,
            'supportEmails': 0,
            'casualEmails': 0,
            'accuracy': 0
        }

        return jsonify(default_stats)

@app.route('/logout')
def logout(): # type: ignore
    """Log out the user and clear session data."""
    global gmail_service, user_info

    # Clear session data
    session.clear()

    # Reset global variables
    gmail_service = None
    user_info = None

    # Show logout message
    flash_message = """
    <div class="success-notice">
        <h3>✅ Logged Out Successfully</h3>
        <p>You have been logged out of your Gmail account.</p>
        <p>You can log in again to continue using the email classifier.</p>
    </div>
    """

    # Redirect to the dashboard
    return render_template('hacker_demo.html', flash_message=flash_message)

@app.route('/api/emails')
def get_email_list():
    """Get list of emails from Gmail and analyze them."""
    global gmail_service, email_analyzer

    # Check if in demo mode
    if 'demo_mode' in session and session['demo_mode']:
        # Return demo data
        demo_emails = [
            {
                'id': 'email-1',
                'subject': 'URGENT: Your Account Has Been Compromised',
                'sender': '<EMAIL>',
                'date': '2023-05-15 14:32',
                'snippet': 'We have detected suspicious activity on your account. Your account access will be terminated unless you verify...',
                'category': 'spam',
                'confidence': 0.92,
                'keywords': ['urgent', 'account', 'compromised', 'verify', 'suspicious']
            },
            {
                'id': 'email-2',
                'subject': 'Quarterly Performance Review Scheduled',
                'sender': '<EMAIL>',
                'date': '2023-05-14 09:15',
                'snippet': 'This is a reminder that your quarterly performance review has been scheduled for next week. Please prepare...',
                'category': 'professional',
                'confidence': 0.88,
                'keywords': ['quarterly', 'performance', 'review', 'scheduled', 'prepare']
            },
            {
                'id': 'email-3',
                'subject': 'Re: Ticket #45678 - Login Issue',
                'sender': '<EMAIL>',
                'date': '2023-05-13 16:45',
                'snippet': 'Thank you for contacting technical support regarding your login issue. Based on our investigation...',
                'category': 'professional',
                'confidence': 0.95,
                'keywords': ['ticket', 'login', 'issue', 'support', 'technical']
            },
            {
                'id': 'email-4',
                'subject': 'Weekend plans?',
                'sender': '<EMAIL>',
                'date': '2023-05-12 18:22',
                'snippet': 'Hey, what are you up to this weekend? Want to grab lunch on Saturday?',
                'category': 'casual',
                'confidence': 0.91,
                'keywords': ['weekend', 'plans', 'lunch', 'saturday', 'grab']
            },
            {
                'id': 'email-5',
                'subject': 'LAST CHANCE: 80% Off Everything!',
                'sender': '<EMAIL>',
                'date': '2023-05-11 10:05',
                'snippet': 'Don\'t miss out on our biggest sale of the year! 80% off everything store-wide. Sale ends tonight!',
                'category': 'spam',
                'confidence': 0.89,
                'keywords': ['chance', 'sale', 'discount', 'offer', 'limited']
            }
        ]

        # Save demo emails to database if user is logged in
        if 'user_id' in session:
            try:
                user_id = session['user_id']
                for email in demo_emails:
                    save_email(email, user_id)

                # Update statistics
                update_statistics(user_id)
            except Exception as db_error:
                logger.error(f"Error saving demo emails to database: {str(db_error)}")

        return jsonify(demo_emails)

    # Check if authenticated
    if gmail_service is None:
        if 'credentials' in session:
            gmail_service, _ = get_gmail_service()
        else:
            # Return empty list if not authenticated
            return jsonify([])

    try:
        # Get query parameters
        max_results = request.args.get('max_results', 10, type=int)
        query = request.args.get('query', '')

        # Check if we should use database or fetch from Gmail
        use_database = request.args.get('use_database', 'true').lower() == 'true'

        if use_database and 'user_id' in session:
            # Get emails from database
            db_emails = get_emails(
                user_id=session['user_id'],
                limit=max_results,
                category=request.args.get('category') # type: ignore
            )
            return jsonify(db_emails)

        # Fetch emails from Gmail
        emails = fetch_emails(gmail_service, max_results=max_results, query=query)

        if not emails:
            return jsonify([])

        # Analyze emails
        analyzed_emails = []
        for email in emails:
            analysis = email_analyzer.analyze_email(email)

            analyzed_email = {
                'id': email['id'],
                'thread_id': email.get('thread_id', ''),
                'subject': email['subject'],
                'sender': email['sender'],
                'recipient': email.get('recipient', ''),
                'date': email['date'],
                'snippet': email.get('snippet', ''),
                'body': email.get('body', ''),
                'category': analysis['category'],
                'confidence': analysis['confidence'],
                'keywords': analysis['keywords'],
                'important': email.get('important', False),
                'unread': email.get('unread', False),
                'gmail_category': email.get('category', 'NONE')
            }

            analyzed_emails.append(analyzed_email)

            # Save to database if user is logged in
            if 'user_id' in session:
                try:
                    save_email(analyzed_email, session['user_id'])
                except Exception as db_error:
                    logger.error(f"Error saving email to database: {str(db_error)}")

        # Update statistics
        if 'user_id' in session:
            try:
                update_statistics(session['user_id'])
            except Exception as stats_error:
                logger.error(f"Error updating statistics: {str(stats_error)}")

        return jsonify(analyzed_emails)

    except Exception as e:
        logger.error(f"Error fetching emails: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/logout')
def logout():
    """Log out from Google."""
    global gmail_service, user_info

    # Clear session
    session.clear()

    # Clear Gmail service
    gmail_service = None
    user_info = None

    # Redirect to the dashboard
    return redirect('/')

# Initialize the classifier when the module is loaded
initialize_classifier()

if __name__ == '__main__':
    # Create data directory if it doesn't exist
    os.makedirs('data/model', exist_ok=True)

    # Run the Flask app
    app.run(debug=True, host='localhost', port=8080)


