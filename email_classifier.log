2025-05-24 11:59:22,641 - root - INFO - Settings loaded from settings.json
2025-05-24 11:59:55,215 - root - INFO - Settings loaded from settings.json
2025-05-24 12:00:39,255 - root - INFO - Settings loaded from settings.json
2025-05-24 12:00:42,295 - email_analyzer - INFO - Loaded email type classifier model from disk
2025-05-24 12:07:28,550 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:08:10,008 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:08:18,886 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:18,887 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:18,888 - root - INFO - Classifier initialized successfully
2025-05-24 12:08:19,335 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 12:08:19,335 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 12:08:19,384 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:08:20,100 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:08:23,250 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:23,252 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:08:23,252 - root - INFO - Classifier initialized successfully
2025-05-24 12:08:23,270 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:08:23,288 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:08:32,151 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:32] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-05-24 12:08:32,476 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:32] "GET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-05-24 12:08:32,535 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:32] "GET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-05-24 12:08:33,106 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:33] "GET /?__debugger__=yes&cmd=resource&f=console.png&s=qT2d5FDWXBEyg1jvgwkl HTTP/1.1" 200 -
2025-05-24 12:08:33,361 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:08:33] "GET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-05-24 12:12:49,900 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\OneDrive\\Desktop\\CAS\\LIVE EMAIL CLASSIFIRE\\app\\routes\\auth.py', reloading
2025-05-24 12:12:50,696 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:12:51,636 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:13:03,959 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:13:06,865 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:06,867 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:06,868 - root - INFO - Classifier initialized successfully
2025-05-24 12:13:06,936 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://localhost:8080
2025-05-24 12:13:06,936 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 12:13:06,942 - werkzeug - INFO -  * Restarting with stat
2025-05-24 12:13:07,487 - root - WARNING - settings.json not found. Will use environment variables.
2025-05-24 12:13:10,415 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:10,417 - classifier.model - INFO - Model and vectorizer loaded successfully
2025-05-24 12:13:10,417 - root - INFO - Classifier initialized successfully
2025-05-24 12:13:10,607 - werkzeug - WARNING -  * Debugger is active!
2025-05-24 12:13:10,628 - werkzeug - INFO -  * Debugger PIN: 624-016-730
2025-05-24 12:13:23,308 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET / HTTP/1.1" 200 -
2025-05-24 12:13:23,592 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/animations.css HTTP/1.1" 200 -
2025-05-24 12:13:23,593 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/styles.css HTTP/1.1" 200 -
2025-05-24 12:13:23,743 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/visualization.css HTTP/1.1" 200 -
2025-05-24 12:13:23,744 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/script.js HTTP/1.1" 200 -
2025-05-24 12:13:23,745 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/css/hacker.css HTTP/1.1" 200 -
2025-05-24 12:13:23,750 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/matrix.js HTTP/1.1" 200 -
2025-05-24 12:13:23,928 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/gmail-integration.js HTTP/1.1" 200 -
2025-05-24 12:13:23,930 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:23] "GET /static/js/visualization.js HTTP/1.1" 200 -
2025-05-24 12:13:24,871 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:24] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-24 12:13:53,331 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "GET /auth HTTP/1.1" 200 -
2025-05-24 12:13:53,565 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:13:53,898 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:13:53,944 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:13:53,988 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:13:53] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:28,885 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:14:28,897 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:14:28,964 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:28] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:14:29,136 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,152 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,468 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,470 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,498 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,533 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,535 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,608 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:14:29,803 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:14:29] "[33mGET /static/img/cyber-grid.png HTTP/1.1[0m" 404 -
2025-05-24 12:35:30,702 - app.services.google_auth - INFO - Running in demo mode
2025-05-24 12:35:30,718 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:30] "GET / HTTP/1.1" 200 -
2025-05-24 12:35:30,922 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:30] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:30,926 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:30] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,226 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,235 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,255 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,259 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:31,264 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:31] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:34,906 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:34] "GET /logout HTTP/1.1" 200 -
2025-05-24 12:35:35,264 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,547 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,576 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,608 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,673 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,675 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,681 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:35,927 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:35] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,113 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "GET /auth HTTP/1.1" 200 -
2025-05-24 12:35:47,403 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,729 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,734 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:47,741 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:47] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:57,602 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:35:57,611 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:35:57,629 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:57] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:35:57,751 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:57] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:57,835 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:57] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,062 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,067 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,070 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,126 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,129 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:35:58,179 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:35:58] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:21,609 - app.services.google_auth - WARNING - Direct password authentication is not implemented for security reasons
2025-05-24 12:36:21,610 - app.services.google_auth - WARNING - Using demo mode instead
2025-05-24 12:36:21,614 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:21] "POST /gmail-auth HTTP/1.1" 200 -
2025-05-24 12:36:21,750 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:21] "[36mGET /static/css/styles.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:21,852 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:21] "[36mGET /static/css/animations.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,096 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/css/hacker.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,101 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/css/visualization.css HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,104 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,141 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/matrix.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,194 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/gmail-integration.js HTTP/1.1[0m" 304 -
2025-05-24 12:36:22,196 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 12:36:22] "[36mGET /static/js/visualization.js HTTP/1.1[0m" 304 -
