"""
<PERSON><PERSON><PERSON> to set up the MySQL database for Email Classifier
"""

import os
import mysql.connector
from mysql.connector import Error
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Database configuration without database name
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': ''
}

DATABASE_NAME = 'email_classifier'
SQL_SCRIPT_PATH = 'database_setup.sql'

def create_database():
    """Create the email_classifier database if it doesn't exist"""
    connection = None
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Create database
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DATABASE_NAME}")
            logger.info(f"Database '{DATABASE_NAME}' created or already exists")
            
            cursor.close()
            return True
    except <PERSON>rror as e:
        logger.error(f"Error creating database: {str(e)}")
        return False
    finally:
        if connection and connection.is_connected():
            connection.close()

def run_sql_script():
    """Run the database_setup.sql script to create tables"""
    if not os.path.exists(SQL_SCRIPT_PATH):
        logger.error(f"SQL script not found: {SQL_SCRIPT_PATH}")
        return False
        
    # Read SQL script
    with open(SQL_SCRIPT_PATH, 'r') as f:
        sql_script = f.read()
    
    # Split script into individual statements
    statements = sql_script.split(';')
    
    # Connect to the database
    connection = None
    try:
        config = DB_CONFIG.copy()
        config['database'] = DATABASE_NAME
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Execute each statement
            for statement in statements:
                if statement.strip():
                    cursor.execute(statement)
            
            connection.commit()
            cursor.close()
            logger.info("Database tables created successfully")
            return True
    except Error as e:
        logger.error(f"Error creating database tables: {str(e)}")
        return False
    finally:
        if connection and connection.is_connected():
            connection.close()

def setup_database():
    """Set up the database and tables"""
    if create_database():
        if run_sql_script():
            logger.info("Database setup completed successfully")
            return True
    
    logger.error("Database setup failed")
    return False

if __name__ == "__main__":
    logger.info("Starting database setup...")
    setup_database()