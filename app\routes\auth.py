"""
Authentication routes for the Live Email Classifier application
"""

from flask import Blueprint, render_template, request, redirect, url_for, session
import logging
import datetime

auth_bp = Blueprint('auth', __name__)
logger = logging.getLogger(__name__)

@auth_bp.route('/auth')
def auth_options():
    """Show authentication options."""
    return render_template('gmail_login.html')

@auth_bp.route('/login')
def login():
    """Start the Google OAuth flow."""
    from app.services.google_auth import start_oauth_flow
    
    # Generate the authorization URL
    authorization_url, state = start_oauth_flow('http://localhost:8080/oauth2callback')

    # Store the state in the session
    session['oauth_state'] = state

    # Redirect to the authorization URL
    return redirect(authorization_url)

@auth_bp.route('/gmail-auth', methods=['GET', 'POST'])
def gmail_auth():
    """Authenticate with Gmail using email and password."""
    from app.services.google_auth import authenticate_with_credentials
    
    if request.method == 'GET':
        return render_template('gmail_login.html')

    # Get form data
    email = request.form.get('email')
    password = request.form.get('password')

    # Authenticate with Gmail
    success, message = authenticate_with_credentials(email, password)

    if success:
        # Set user info
        user_info = {
            'email': email,
            'messages_total': 1247,  # Placeholder
            'threads_total': 356     # Placeholder
        }

        # Store credentials in session
        session['credentials'] = True

        # Check if we're in demo mode
        if 'Using demo mode' in message:
            session['demo_mode'] = True

            # Show a flash message
            flash_message = f"""
            <div class="demo-notice">
                <h3>⚠️ Demo Mode Active</h3>
                <p>{message}</p>
                <p>For security reasons, Google requires OAuth2 for Gmail access.</p>
                <p>To use real Gmail integration:</p>
                <ol>
                    <li>Create a project in the <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
                    <li>Enable the Gmail API</li>
                    <li>Create OAuth 2.0 credentials</li>
                    <li>Download as client_secret.json and place in the project root</li>
                </ol>
            </div>
            """

            # Redirect to the dashboard
            return render_template('hacker_demo.html',
                                user_info=user_info,
                                demo_mode=True,
                                flash_message=flash_message)

        # Redirect to the dashboard
        return redirect('/')
    else:
        # Show error message
        return render_template('gmail_login.html', error=message)

@auth_bp.route('/demo-auth')
def demo_auth():
    """Demo authentication for when OAuth credentials are not available."""
    # Set demo user info
    user_info = {
        'email': '<EMAIL>',
        'messages_total': 1247,
        'threads_total': 356
    }

    # Store credentials in session
    session['credentials'] = True
    session['demo_mode'] = True

    # Show a flash message
    flash_message = """
    <div class="demo-notice">
        <h3>⚠️ Demo Mode Active</h3>
        <p>Using demo authentication because no valid Google OAuth credentials were found.</p>
        <p>To use real Gmail integration:</p>
        <ol>
            <li>Create a project in the <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
            <li>Enable the Gmail API</li>
            <li>Create OAuth 2.0 credentials</li>
            <li>Download as client_secret.json and place in the project root</li>
        </ol>
    </div>
    """

    # Redirect to the dashboard
    return render_template('hacker_demo.html',
                          user_info=user_info,
                          demo_mode=True,
                          flash_message=flash_message)

@auth_bp.route('/logout')
def logout():
    """Log out the user and clear session data."""
    # Clear session data
    session.clear()

    # Show logout message
    flash_message = """
    <div class="success-notice">
        <h3>✅ Logged Out Successfully</h3>
        <p>You have been logged out of your Gmail account.</p>
        <p>You can log in again to continue using the email classifier.</p>
    </div>
    """

    # Redirect to the dashboard
    return render_template('hacker_demo.html', flash_message=flash_message)
