"""
Authentication routes for the Live Email Classifier application
"""

from flask import Blueprint, render_template, request, redirect, url_for, session
import logging
import datetime

auth_bp = Blueprint('auth', __name__)
logger = logging.getLogger(__name__)

@auth_bp.route('/auth')
def auth_options():
    """Show authentication options."""
    return render_template('gmail_login.html')

@auth_bp.route('/login')
def login():
    """Start the Google OAuth flow."""
    from app.services.google_auth import start_oauth_flow

    # Generate the authorization URL
    authorization_url, state = start_oauth_flow('http://localhost:8080/oauth2callback')

    # Store the state in the session
    session['oauth_state'] = state

    # Redirect to the authorization URL
    return redirect(authorization_url)

@auth_bp.route('/gmail-auth', methods=['GET', 'POST'])
def gmail_auth():
    """Authenticate with Gmail using email and password."""
    from app.services.google_auth import authenticate_with_credentials

    if request.method == 'GET':
        return render_template('gmail_login.html')

    # Get form data
    email = request.form.get('email')
    password = request.form.get('password')

    # Authenticate with Gmail
    success, message = authenticate_with_credentials(email, password)

    if success:
        # Set user info
        user_info = {
            'email': email,
            'messages_total': 1247,  # Placeholder
            'threads_total': 356     # Placeholder
        }

        # Store credentials in session
        session['credentials'] = True

        # Check if we're in demo mode
        if 'Using demo mode' in message:
            session['demo_mode'] = True

            # Show a flash message
            flash_message = f"""
            <div class="demo-notice">
                <h3>⚠️ Demo Mode Active</h3>
                <p>{message}</p>
                <p>For security reasons, Google requires OAuth2 for Gmail access.</p>
                <p>To use real Gmail integration:</p>
                <ol>
                    <li>Create a project in the <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
                    <li>Enable the Gmail API</li>
                    <li>Create OAuth 2.0 credentials</li>
                    <li>Download as client_secret.json and place in the project root</li>
                </ol>
            </div>
            """

            # Redirect to the dashboard
            return render_template('hacker_demo.html',
                                user_info=user_info,
                                demo_mode=True,
                                flash_message=flash_message)

        # Redirect to the dashboard
        return redirect('/')
    else:
        # Show error message
        return render_template('gmail_login.html', error=message)

@auth_bp.route('/demo-auth')
def demo_auth():
    """Demo authentication for when OAuth credentials are not available."""
    # Set demo user info
    user_info = {
        'email': '<EMAIL>',
        'messages_total': 1247,
        'threads_total': 356
    }

    # Store credentials in session
    session['credentials'] = True
    session['demo_mode'] = True

    # Show a flash message
    flash_message = """
    <div class="demo-notice">
        <h3>⚠️ Demo Mode Active</h3>
        <p>Using demo authentication because no valid Google OAuth credentials were found.</p>
        <p>To use real Gmail integration:</p>
        <ol>
            <li>Create a project in the <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
            <li>Enable the Gmail API</li>
            <li>Create OAuth 2.0 credentials</li>
            <li>Download as client_secret.json and place in the project root</li>
        </ol>
    </div>
    """

    # Redirect to the dashboard
    return render_template('hacker_demo.html',
                          user_info=user_info,
                          demo_mode=True,
                          flash_message=flash_message)

@auth_bp.route('/oauth2callback')
def oauth2callback():
    """Handle the OAuth callback from Google."""
    from app.services.google_auth import complete_oauth_flow, get_gmail_service
    from app.services.database import get_user_by_email, create_user, update_user, update_statistics

    # Check if state matches
    if 'oauth_state' not in session:
        flash_message = """
        <div class="error-notice">
            <h3>⚠️ Authentication Error</h3>
            <p>Invalid state parameter. This could be due to an expired session or a security issue.</p>
            <p>Please try authenticating again.</p>
        </div>
        """
        return render_template('hacker_demo.html', flash_message=flash_message)

    state = session['oauth_state']

    # Log the callback URL for debugging
    logger.info(f"OAuth callback received: {request.url}")

    # Complete the OAuth flow
    success, oauth_user_info = complete_oauth_flow(
        'http://localhost:8080/oauth2callback',
        state,
        request.url
    )

    if not success:
        flash_message = """
        <div class="error-notice">
            <h3>⚠️ Authentication Error</h3>
            <p>Failed to complete the authentication process with Google.</p>
            <p>Please try again or contact support if the issue persists.</p>
        </div>
        """
        logger.error("OAuth flow completion failed")
        return render_template('hacker_demo.html', flash_message=flash_message)

    # Store credentials and user info in session
    session['credentials'] = True
    session['demo_mode'] = False

    # Handle the case where oauth_user_info might be None
    if oauth_user_info is None:
        oauth_user_info = {
            'email': '<EMAIL>',
            'messages_total': 0,
            'threads_total': 0
        }
        logger.warning("OAuth user info is None, using default values")

    email_address = oauth_user_info.get('email', '<EMAIL>')
    session['user_email'] = email_address

    # Set user info
    user_info = oauth_user_info
    logger.info(f"User authenticated: {email_address}")

    # Get the Gmail service
    gmail_service, _ = get_gmail_service()

    # Store user in database
    try:
        # Check if user exists
        db_user = get_user_by_email(email_address)

        # Prepare user data
        user_data = {
            'email': email_address,
            'name': email_address.split('@')[0],  # Simple name extraction
            'profile_picture': '',
            'access_token': session.get('access_token', ''),
            'refresh_token': session.get('refresh_token', ''),
            'token_expiry': datetime.datetime.now() + datetime.timedelta(hours=1)
        }

        if db_user:
            # Update existing user
            update_user(db_user['id'], user_data)
            session['user_id'] = db_user['id']
            logger.info(f"Updated user in database: {email_address}")
        else:
            # Create new user
            user_id = create_user(user_data)
            session['user_id'] = user_id
            logger.info(f"Created new user in database: {email_address}")

        # Update statistics
        user_id = session.get('user_id')
        if user_id is not None:
            update_statistics(user_id)

    except Exception as e:
        logger.error(f"Error storing user in database: {str(e)}")

    # Show success message
    email_address = user_info.get('email', 'your Gmail account')
    flash_message = f"""
    <div class="success-notice">
        <h3>✅ Authentication Successful</h3>
        <p>Successfully connected to Gmail as {email_address}</p>
        <p>You can now analyze your emails using the classifier.</p>
    </div>
    """

    # Redirect to the dashboard
    return render_template('hacker_demo.html',
                          user_info=user_info,
                          flash_message=flash_message)

@auth_bp.route('/logout')
def logout():
    """Log out the user and clear session data."""
    # Clear session data
    session.clear()

    # Show logout message
    flash_message = """
    <div class="success-notice">
        <h3>✅ Logged Out Successfully</h3>
        <p>You have been logged out of your Gmail account.</p>
        <p>You can log in again to continue using the email classifier.</p>
    </div>
    """

    # Redirect to the dashboard
    return render_template('hacker_demo.html', flash_message=flash_message)
