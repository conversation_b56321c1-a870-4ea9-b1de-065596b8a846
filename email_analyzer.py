"""
Email analyzer module for the Live Email Classifier.
This module analyzes emails and classifies them as spam, professional, or casual.
"""

import re
import logging
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.pipeline import Pipeline
import numpy as np
import pickle
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('email_classifier.log')
    ]
)

logger = logging.getLogger(__name__)

# Download NLTK resources if not already downloaded
try:
    nltk.data.find('tokenizers/punkt')
    nltk.data.find('corpora/stopwords')
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('punkt')
    nltk.download('stopwords')
    nltk.download('wordnet')

# Constants
MODEL_PATH = 'data/email_type_classifier.pkl'
CATEGORIES = ['spam', 'professional', 'casual']

class EmailAnalyzer:
    """Class for analyzing and classifying emails."""
    
    def __init__(self):
        """Initialize the EmailAnalyzer."""
        self.model = None
        self.stop_words = set(stopwords.words('english'))
        self.lemmatizer = WordNetLemmatizer()
        
        # Load or train the model
        self._load_or_train_model()
    
    def _load_or_train_model(self):
        """Load the model from disk or train a new one if it doesn't exist."""
        if os.path.exists(MODEL_PATH):
            try:
                with open(MODEL_PATH, 'rb') as f:
                    self.model = pickle.load(f)
                logger.info("Loaded email type classifier model from disk")
            except Exception as e:
                logger.error(f"Error loading model: {str(e)}")
                self._train_model()
        else:
            self._train_model()
    
    def _train_model(self):
        """Train a new model for email type classification."""
        logger.info("Training new email type classifier model")
        
        # Sample training data
        training_data = [
            # Spam examples
            ("Get rich quick! Limited time offer!", "spam"),
            ("URGENT: Your account has been compromised", "spam"),
            ("Congratulations! You've won a free iPhone", "spam"),
            ("Click here to claim your prize now", "spam"),
            ("Increase your followers on social media", "spam"),
            ("Buy now and get 50% off", "spam"),
            ("Your payment is overdue. Click here", "spam"),
            ("You have been selected for a special offer", "spam"),
            ("Verify your account information immediately", "spam"),
            ("Amazing weight loss solution", "spam"),
            
            # Professional examples
            ("Regarding our meeting scheduled for tomorrow", "professional"),
            ("Please find attached the quarterly report", "professional"),
            ("I'm writing to follow up on our discussion", "professional"),
            ("Thank you for your prompt response", "professional"),
            ("We would like to schedule a call to discuss", "professional"),
            ("I'm pleased to inform you that your application has been accepted", "professional"),
            ("Please review the attached document and provide feedback", "professional"),
            ("This is a reminder about the upcoming deadline", "professional"),
            ("I'm writing to confirm receipt of your payment", "professional"),
            ("We are reaching out regarding your recent inquiry", "professional"),
            
            # Casual examples
            ("Hey, what's up?", "casual"),
            ("Can we grab lunch tomorrow?", "casual"),
            ("Just checking in to see how you're doing", "casual"),
            ("Thanks for the birthday wishes!", "casual"),
            ("Let me know if you want to hang out this weekend", "casual"),
            ("Did you see that movie I recommended?", "casual"),
            ("I'm running late, be there in 10", "casual"),
            ("Hey, can you send me that photo from yesterday?", "casual"),
            ("Don't forget to bring the snacks!", "casual"),
            ("Are you free to chat later today?", "casual")
        ]
        
        # Extract texts and labels
        texts, labels = zip(*training_data)
        
        # Create and train the model
        self.model = Pipeline([
            ('vectorizer', TfidfVectorizer(max_features=5000)),
            ('classifier', MultinomialNB())
        ])
        
        self.model.fit(texts, labels)
        
        # Save the model
        os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)
        with open(MODEL_PATH, 'wb') as f:
            pickle.dump(self.model, f)
        
        logger.info("Trained and saved new email type classifier model")
    
    def preprocess_text(self, text):
        """
        Preprocess text for classification.
        
        Args:
            text: Raw text to preprocess
            
        Returns:
            str: Preprocessed text
        """
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove URLs
        text = re.sub(r'https?://\S+|www\.\S+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove special characters and numbers
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        
        # Tokenize
        tokens = word_tokenize(text)
        
        # Remove stopwords and lemmatize
        tokens = [self.lemmatizer.lemmatize(token) for token in tokens if token not in self.stop_words and len(token) > 2]
        
        # Join tokens back into a string
        preprocessed_text = ' '.join(tokens)
        
        return preprocessed_text
    
    def analyze_email(self, email_data):
        """
        Analyze an email and classify it.
        
        Args:
            email_data: Dictionary containing email data
            
        Returns:
            dict: Analysis results
        """
        try:
            # Extract text from email data
            text = ""
            if 'subject' in email_data and email_data['subject']:
                text += email_data['subject'] + " "
            
            if 'body' in email_data and email_data['body']:
                text += email_data['body']
            
            if 'snippet' in email_data and email_data['snippet']:
                text += email_data['snippet']
            
            # Preprocess the text
            preprocessed_text = self.preprocess_text(text)
            
            if not preprocessed_text:
                return {
                    'category': 'unknown',
                    'confidence': 0.0,
                    'keywords': []
                }
            
            # Classify the email
            if self.model:
                # Get prediction probabilities
                probabilities = self.model.predict_proba([preprocessed_text])[0]
                
                # Get the highest probability and its index
                max_prob = max(probabilities)
                max_prob_index = np.argmax(probabilities)
                
                # Get the predicted category
                predicted_category = CATEGORIES[max_prob_index]
                
                # Extract keywords (simple implementation)
                words = preprocessed_text.split()
                keywords = list(set([word for word in words if len(word) > 3]))[:5]
                
                return {
                    'category': predicted_category,
                    'confidence': float(max_prob),
                    'keywords': keywords,
                    'probabilities': {cat: float(prob) for cat, prob in zip(CATEGORIES, probabilities)}
                }
            else:
                logger.error("Model not initialized")
                return {
                    'category': 'unknown',
                    'confidence': 0.0,
                    'keywords': []
                }
        
        except Exception as e:
            logger.error(f"Error analyzing email: {str(e)}")
            return {
                'category': 'unknown',
                'confidence': 0.0,
                'keywords': [],
                'error': str(e)
            }
    
    def batch_analyze_emails(self, emails):
        """
        Analyze a batch of emails.
        
        Args:
            emails: List of email data dictionaries
            
        Returns:
            list: List of analysis results
        """
        results = []
        for email in emails:
            result = self.analyze_email(email)
            results.append({
                'email': email,
                'analysis': result
            })
        
        return results
