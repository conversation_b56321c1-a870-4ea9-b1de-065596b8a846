"""
Google OAuth2 authentication module for the Live Email Classifier.
This module handles authentication with Google's API to access Gmail.
"""

import os
import pickle
import base64
import json
import logging
from google_auth_oauthlib.flow import Flow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('email_classifier.log')
    ]
)

logger = logging.getLogger(__name__)

# If modifying these scopes, delete the token.pickle file
SCOPES = [
    'https://www.googleapis.com/auth/gmail.readonly',  # Read-only access to Gmail
    'https://www.googleapis.com/auth/gmail.labels',    # Access to manage labels
    'https://www.googleapis.com/auth/gmail.metadata',  # Access to email metadata
    'https://www.googleapis.com/auth/userinfo.email'   # Access to user email address
]

# Path to store the OAuth token
TOKEN_PATH = 'token.pickle'

# Path to store the OAuth flow
FLOW_PATH = 'oauth_flow.pickle'

# Path to store the client secrets file
# You need to download this from Google Cloud Console
CLIENT_SECRETS_FILE = 'client_secret.json'

# Demo mode flag
DEMO_MODE = False

def get_gmail_service():
    """
    Get an authorized Gmail API service instance.

    Returns:
        service: Authorized Gmail API service instance
        user_info: User information (email, name)
    """
    global DEMO_MODE
    creds = None
    user_info = None

    # Check if we're in demo mode
    if DEMO_MODE:
        logger.info("Running in demo mode")
        return None, {
            'email': '<EMAIL>',
            'messages_total': 1247,
            'threads_total': 356
        }

    try:
        # Check if token.pickle exists
        if os.path.exists(TOKEN_PATH):
            with open(TOKEN_PATH, 'rb') as token:
                try:
                    creds = pickle.load(token)
                    logger.info("Loaded credentials from token.pickle")
                except Exception as e:
                    logger.error(f"Error loading credentials: {str(e)}")

        # If credentials don't exist or are invalid, get new ones
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    logger.info("Refreshed expired credentials")
                except Exception as e:
                    logger.error(f"Error refreshing credentials: {str(e)}")
                    creds = None

            # If still no valid credentials, need to authenticate
            if not creds:
                return None, None

        # Save the credentials for the next run
        with open(TOKEN_PATH, 'wb') as token:
            pickle.dump(creds, token)

        # Build the Gmail service
        service = build('gmail', 'v1', credentials=creds)

        # Get user information
        profile = service.users().getProfile(userId='me').execute()

        # Get email statistics
        messages_response = service.users().messages().list(userId='me', maxResults=1).execute()
        threads_response = service.users().threads().list(userId='me', maxResults=1).execute()

        user_info = {
            'email': profile.get('emailAddress', ''),
            'messages_total': profile.get('messagesTotal', messages_response.get('resultSizeEstimate', 0)),
            'threads_total': profile.get('threadsTotal', threads_response.get('resultSizeEstimate', 0))
        }

        logger.info(f"Successfully authenticated as {user_info['email']}")
        return service, user_info

    except Exception as e:
        logger.error(f"Error building Gmail service: {str(e)}")
        return None, None

def start_oauth_flow(redirect_uri):
    """
    Start the OAuth flow to authenticate with Google.

    Args:
        redirect_uri: The URI to redirect to after authentication

    Returns:
        authorization_url: The URL to redirect the user to for authentication
        state: The state parameter to verify the redirect
    """
    try:
        # Check if client_secret.json exists and is valid
        if not os.path.exists(CLIENT_SECRETS_FILE):
            logger.error(f"Client secrets file not found: {CLIENT_SECRETS_FILE}")
            global DEMO_MODE
            DEMO_MODE = True
            return "/demo-auth", "demo"

        # Check if it's a placeholder file
        with open(CLIENT_SECRETS_FILE, 'r') as f:
            content = f.read()
            if "YOUR_CLIENT_ID" in content or "YOUR_CLIENT_SECRET" in content:
                logger.warning("Using placeholder client_secret.json. OAuth will not work properly.")
                DEMO_MODE = True
                return "/demo-auth", "demo"

        # Load client configuration
        with open(CLIENT_SECRETS_FILE, 'r') as f:
            client_config = json.load(f)

        # Create the flow using the client config
        flow = Flow.from_client_config(
            client_config,
            scopes=SCOPES,
            redirect_uri=redirect_uri
        )

        # Generate the authorization URL with optimal settings for Gmail access
        authorization_url, state = flow.authorization_url(
            # Request offline access to get a refresh token
            access_type='offline',
            # Include previously granted scopes
            include_granted_scopes='true',
            # Force showing the consent screen to ensure getting refresh token
            prompt='consent'
        )

        # Save flow to pickle file for later use
        with open(FLOW_PATH, 'wb') as f:
            pickle.dump(flow, f)

        logger.info("OAuth flow started successfully")
        return authorization_url, state

    except Exception as e:
        logger.error(f"Error starting OAuth flow: {str(e)}")
        DEMO_MODE = True
        return "/demo-auth", "demo"

def complete_oauth_flow(redirect_uri, state, authorization_response):
    """
    Complete the OAuth flow after the user has authenticated.

    Args:
        redirect_uri: The URI that was used for the initial request
        state: The state parameter from the initial request
        authorization_response: The full redirect URL with the authorization code

    Returns:
        success: True if authentication was successful, False otherwise
        user_info: User information if successful, None otherwise
    """
    global DEMO_MODE

    # If in demo mode, just return success
    if DEMO_MODE or state == "demo":
        logger.info("Demo mode: Skipping OAuth completion")
        return True, {
            'email': '<EMAIL>',
            'messages_total': 1247,
            'threads_total': 356
        }

    try:
        # Load flow from pickle file
        if not os.path.exists(FLOW_PATH):
            logger.error("OAuth flow not found. Please start the flow again.")
            return False, None

        with open(FLOW_PATH, 'rb') as f:
            flow = pickle.load(f)

        # Verify redirect URI
        flow.redirect_uri = redirect_uri

        # Fetch token
        flow.fetch_token(authorization_response=authorization_response)

        # Get credentials
        credentials = flow.credentials

        # Save credentials to file
        with open(TOKEN_PATH, 'wb') as f:
            pickle.dump(credentials, f)

        # Build Gmail service
        service = build('gmail', 'v1', credentials=credentials)

        # Get user profile
        profile = service.users().getProfile(userId='me').execute()

        # Get email statistics
        messages_response = service.users().messages().list(userId='me', maxResults=1).execute()
        threads_response = service.users().threads().list(userId='me', maxResults=1).execute()

        # Create user info
        user_info = {
            'email': profile.get('emailAddress', ''),
            'messages_total': profile.get('messagesTotal', messages_response.get('resultSizeEstimate', 0)),
            'threads_total': profile.get('threadsTotal', threads_response.get('resultSizeEstimate', 0))
        }

        # Clean up flow pickle file
        if os.path.exists(FLOW_PATH):
            os.remove(FLOW_PATH)

        logger.info(f"Successfully completed OAuth flow for {user_info['email']}")
        return True, user_info

    except Exception as e:
        logger.error(f"Error completing OAuth flow: {str(e)}")
        DEMO_MODE = True
        return False, None

def fetch_emails(service, max_results=10, query=''):
    """
    Fetch emails from Gmail.

    Args:
        service: Authorized Gmail API service instance
        max_results: Maximum number of emails to fetch (default: 10)
        query: Gmail search query (default: '')

    Returns:
        emails: List of email data dictionaries
    """
    global DEMO_MODE

    # If in demo mode, return demo emails
    if DEMO_MODE or service is None:
        logger.info("Demo mode: Returning demo emails")
        return get_demo_emails()

    try:
        # Construct a more specific query if none provided
        if not query:
            # Default query to get a mix of different types of emails
            # Exclude trash and spam by default
            query = "in:inbox OR in:sent -in:trash -in:spam"

        # Get list of messages
        results = service.users().messages().list(
            userId='me',
            maxResults=max_results,
            q=query
        ).execute()

        messages = results.get('messages', [])

        if not messages:
            logger.info("No messages found.")
            return []

        # Fetch each message with batch processing to improve performance
        emails = []
        batch_size = 5  # Process emails in batches of 5

        for i in range(0, len(messages), batch_size):
            batch = messages[i:i+batch_size]

            for message in batch:
                try:
                    # Get full message with headers and body
                    msg = service.users().messages().get(
                        userId='me',
                        id=message['id'],
                        format='full'
                    ).execute()

                    # Extract email data
                    email_data = parse_email(msg)
                    emails.append(email_data)
                except Exception as msg_error:
                    logger.warning(f"Error fetching message {message['id']}: {str(msg_error)}")
                    continue

        logger.info(f"Successfully fetched {len(emails)} emails")
        return emails

    except Exception as e:
        logger.error(f"Error fetching emails: {str(e)}")
        # Fall back to demo emails if there's an error
        return get_demo_emails()

def get_demo_emails():
    """
    Get demo emails for testing.

    Returns:
        emails: List of demo email data dictionaries
    """
    return [
        {
            'id': 'email-1',
            'thread_id': 'thread-1',
            'subject': 'URGENT: Your Account Has Been Compromised',
            'sender': '<EMAIL>',
            'recipient': '<EMAIL>',
            'date': '2023-05-15 14:32',
            'body': 'Dear Valued Customer,\n\nWe have detected suspicious activity on your account. Your account access will be terminated unless you verify your information immediately. Click the link below to confirm your identity and restore access.\n\n[SUSPICIOUS LINK REMOVED]\n\nThis is an urgent matter that requires your immediate attention.\n\nSecurity Team',
            'snippet': 'We have detected suspicious activity on your account. Your account access will be terminated unless you verify...',
            'labels': ['INBOX', 'UNREAD'],
            'important': False,
            'unread': True,
            'category': 'NONE'
        },
        {
            'id': 'email-2',
            'thread_id': 'thread-2',
            'subject': 'Quarterly Performance Review Scheduled',
            'sender': '<EMAIL>',
            'recipient': '<EMAIL>',
            'date': '2023-05-14 09:15',
            'body': 'Hello Team Member,\n\nThis is a reminder that your quarterly performance review has been scheduled for next week. Please prepare your self-assessment and project highlights for the past quarter.\n\nThe meeting will take place on Tuesday at 2:00 PM in the conference room. If you need to reschedule, please contact HR as soon as possible.\n\nBest regards,\nHuman Resources Department',
            'snippet': 'This is a reminder that your quarterly performance review has been scheduled for next week. Please prepare...',
            'labels': ['INBOX', 'IMPORTANT'],
            'important': True,
            'unread': False,
            'category': 'NONE'
        },
        {
            'id': 'email-3',
            'thread_id': 'thread-3',
            'subject': 'Re: Ticket #45678 - Login Issue',
            'sender': '<EMAIL>',
            'recipient': '<EMAIL>',
            'date': '2023-05-13 16:45',
            'body': 'Hello,\n\nThank you for contacting technical support regarding your login issue. Based on our investigation, it appears that your account was temporarily locked due to multiple failed login attempts.\n\nWe have reset your account status. Please try logging in again with your current credentials. If you continue to experience issues, please let us know.\n\nTechnical Support Team',
            'snippet': 'Thank you for contacting technical support regarding your login issue. Based on our investigation...',
            'labels': ['INBOX', 'CATEGORY_PERSONAL'],
            'important': False,
            'unread': False,
            'category': 'PERSONAL'
        },
        {
            'id': 'email-4',
            'thread_id': 'thread-4',
            'subject': 'Weekend plans?',
            'sender': '<EMAIL>',
            'recipient': '<EMAIL>',
            'date': '2023-05-12 18:22',
            'body': 'Hey,\n\nWhat are you up to this weekend? Want to grab lunch on Saturday?\n\nLet me know!\n\nCheers',
            'snippet': 'Hey, what are you up to this weekend? Want to grab lunch on Saturday?',
            'labels': ['INBOX', 'CATEGORY_PERSONAL'],
            'important': False,
            'unread': False,
            'category': 'PERSONAL'
        },
        {
            'id': 'email-5',
            'thread_id': 'thread-5',
            'subject': 'LAST CHANCE: 80% Off Everything!',
            'sender': '<EMAIL>',
            'recipient': '<EMAIL>',
            'date': '2023-05-11 10:05',
            'body': 'Don\'t miss out on our biggest sale of the year! 80% off everything store-wide. Sale ends tonight!\n\nShop now at our website: [LINK]\n\nUnsubscribe | View in browser',
            'snippet': 'Don\'t miss out on our biggest sale of the year! 80% off everything store-wide. Sale ends tonight!',
            'labels': ['INBOX', 'CATEGORY_PROMOTIONS'],
            'important': False,
            'unread': False,
            'category': 'PROMOTIONS'
        }
    ]

def parse_email(msg):
    """
    Parse a Gmail message into a structured format.

    Args:
        msg: Gmail message object

    Returns:
        email_data: Dictionary containing email data
    """
    try:
        # Get headers
        headers = msg['payload']['headers']
        subject = next((h['value'] for h in headers if h['name'].lower() == 'subject'), 'No Subject')
        sender = next((h['value'] for h in headers if h['name'].lower() == 'from'), 'Unknown Sender')
        date = next((h['value'] for h in headers if h['name'].lower() == 'date'), '')
        to = next((h['value'] for h in headers if h['name'].lower() == 'to'), '')

        # Get body
        body = ''

        # Function to decode base64 data
        def decode_base64(data):
            if not data:
                return ''
            try:
                # Add padding if needed
                padded_data = data + '=' * (4 - len(data) % 4) if len(data) % 4 else data
                return base64.urlsafe_b64decode(padded_data).decode('utf-8')
            except Exception as decode_error:
                logger.warning(f"Error decoding base64: {str(decode_error)}")
                return ''

        # Extract body text recursively from message parts
        def get_text_from_parts(parts):
            text = ''
            for part in parts:
                if part.get('mimeType') == 'text/plain':
                    part_body = part.get('body', {})
                    if 'data' in part_body:
                        text += decode_base64(part_body['data'])
                elif part.get('mimeType') == 'multipart/alternative' and 'parts' in part:
                    text += get_text_from_parts(part['parts'])
                elif 'parts' in part:
                    text += get_text_from_parts(part['parts'])
            return text

        # Try to get body from parts
        if 'parts' in msg['payload']:
            body = get_text_from_parts(msg['payload']['parts'])
        # If no parts or no text found, try to get from body directly
        elif 'body' in msg['payload'] and 'data' in msg['payload']['body']:
            body = decode_base64(msg['payload']['body']['data'])

        # If still no body, use snippet
        if not body.strip():
            body = msg.get('snippet', '')

        # Get labels
        labels = msg.get('labelIds', [])

        # Create email data dictionary
        email_data = {
            'id': msg['id'],
            'thread_id': msg['threadId'],
            'subject': subject,
            'sender': sender,
            'recipient': to,
            'date': date,
            'body': body,
            'labels': labels,
            'snippet': msg.get('snippet', ''),
            'important': 'IMPORTANT' in labels,
            'unread': 'UNREAD' in labels,
            'category': next((label.replace('CATEGORY_', '') for label in labels if label.startswith('CATEGORY_')), 'NONE')
        }

        return email_data

    except Exception as e:
        logger.error(f"Error parsing email: {str(e)}")
        return {
            'id': msg.get('id', 'unknown'),
            'subject': 'Error parsing email',
            'sender': 'Unknown',
            'recipient': '',
            'date': '',
            'body': '',
            'labels': [],
            'snippet': 'Error parsing email',
            'important': False,
            'unread': False,
            'category': 'NONE'
        }

def authenticate_with_credentials(email, password):
    """
    Authenticate with Gmail using email and app password.

    Args:
        email: Gmail email address
        password: Gmail app password

    Returns:
        success: True if authentication was successful, False otherwise
        error: Error message if authentication failed, None otherwise
    """
    global DEMO_MODE

    # Validate inputs
    if not email or not password:
        return False, "Email and password are required"

    if not email.endswith('@gmail.com'):
        return False, "Only Gmail addresses are supported"

    try:
        # For security reasons, we can't actually implement direct password authentication
        # Google doesn't allow this anymore and requires OAuth2
        # We'll simulate success for demonstration purposes

        logger.warning("Direct password authentication is not implemented for security reasons")
        logger.warning("Using demo mode instead")

        # Set demo mode
        DEMO_MODE = True

        # Return success with a warning
        return True, "Using demo mode: Direct password authentication is not implemented for security reasons"

    except Exception as e:
        logger.error(f"Error authenticating with credentials: {str(e)}")
        DEMO_MODE = True
        return False, str(e)
