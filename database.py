"""
Database module for the Live Email Classifier.
Handles database connections and operations.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Tuple, Union, cast, Sequence

import mysql.connector
from mysql.connector import Error

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('email_classifier.log')
    ]
)

logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'email_classifier'
}

def get_connection():
    """
    Get a connection to the MySQL database.
    
    Returns:
        mysql.connector.connection.MySQLConnection: Database connection
    """
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            logger.info("Connected to MySQL database")
            return connection
    except Error as e:
        logger.error(f"Error connecting to MySQL database: {str(e)}")
        return None

def close_connection(connection):
    """
    Close a database connection.
    
    Args:
        connection: Database connection to close
    """
    if connection and connection.is_connected():
        connection.close()
        logger.info("MySQL connection closed")

def execute_query(query: str, params: Optional[Tuple] = None, fetch: bool = False) -> Union[List[Dict[str, Any]], int, None]:
    """
    Execute a SQL query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        fetch: Whether to fetch results
        
    Returns:
        List of dictionaries with query results, or number of affected rows, or None if error
    """
    connection = get_connection()
    cursor = None
    result = None
    
    try:
        if connection:
            cursor = connection.cursor(dictionary=True)
            cursor.execute(query, params or ())
            
            if fetch:
                result = cursor.fetchall()
            else:
                connection.commit()
                result = cursor.rowcount
                
            logger.info(f"Query executed successfully: {query[:50]}...")
            
    except Error as e:
        logger.error(f"Error executing query: {str(e)}")
        if connection:
            connection.rollback()
            
    finally:
        if cursor:
            cursor.close()
        close_connection(connection)
        
    return result # type: ignore

# User operations
def get_user_by_email(email: str) -> Optional[Dict[str, Any]]:
    """
    Get a user by email.
    
    Args:
        email: User's email address
        
    Returns:
        User data dictionary or None if not found
    """
    query = "SELECT * FROM users WHERE email = %s"
    result = execute_query(query, (email,), fetch=True)
    
    if result and isinstance(result, list) and len(result) > 0:
        return result[0]
    return None

def create_user(user_data: Dict[str, Any]) -> int:
    """
    Create a new user.
    
    Args:
        user_data: User data dictionary
        
    Returns:
        User ID if successful, 0 if failed
    """
    query = """
    INSERT INTO users (email, name, profile_picture, access_token, refresh_token, token_expiry)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    params = (
        user_data.get('email', ''),
        user_data.get('name', ''),
        user_data.get('profile_picture', ''),
        user_data.get('access_token', ''),
        user_data.get('refresh_token', ''),
        user_data.get('token_expiry')
    )
    
    result = execute_query(query, params)
    
    if result and isinstance(result, int) and result > 0:
        # Get the user ID
        email = user_data.get('email')
        if email:
            user = get_user_by_email(email)
            if user:
                return user['id']
    return 0

def update_user(user_id: int, user_data: Dict[str, Any]) -> bool:
    """
    Update a user.
    
    Args:
        user_id: User ID
        user_data: User data dictionary
        
    Returns:
        True if successful, False if failed
    """
    query = """
    UPDATE users SET 
        name = %s,
        profile_picture = %s,
        access_token = %s,
        refresh_token = %s,
        token_expiry = %s
    WHERE id = %s
    """
    params = (
        user_data.get('name', ''),
        user_data.get('profile_picture', ''),
        user_data.get('access_token', ''),
        user_data.get('refresh_token', ''),
        user_data.get('token_expiry'),
        user_id
    )
    
    result = execute_query(query, params)
    
    return bool(result and isinstance(result, int) and result > 0)

# Email operations
def save_email(email_data: Dict[str, Any], user_id: Optional[int] = None) -> bool:
    """
    Save an email to the database.
    
    Args:
        email_data: Email data dictionary
        user_id: User ID
        
    Returns:
        True if successful, False if failed
    """
    # Check if email already exists
    email_id = email_data.get('id', '')
    if not email_id:
        logger.error("Cannot save email without an ID")
        return False
        
    existing = execute_query(
        "SELECT id FROM emails WHERE id = %s",
        (email_id,),
        fetch=True
    )
    
    if existing and isinstance(existing, list) and len(existing) > 0:
        # Update existing email
        query = """
        UPDATE emails SET
            subject = %s,
            sender = %s,
            recipient = %s,
            date = %s,
            snippet = %s,
            body = %s,
            category = %s,
            confidence = %s,
            is_important = %s,
            is_unread = %s,
            gmail_category = %s
        WHERE id = %s
        """
        params = (
            email_data.get('subject', ''),
            email_data.get('sender', ''),
            email_data.get('recipient', ''),
            email_data.get('date'),
            email_data.get('snippet', ''),
            email_data.get('body', ''),
            email_data.get('category', ''),
            email_data.get('confidence'),
            email_data.get('important', False),
            email_data.get('unread', False),
            email_data.get('gmail_category', ''),
            email_id
        )
    else:
        # Insert new email
        query = """
        INSERT INTO emails (
            id, user_id, thread_id, subject, sender, recipient, 
            date, snippet, body, category, confidence, 
            is_important, is_unread, gmail_category
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            email_id,
            user_id,
            email_data.get('thread_id', ''),
            email_data.get('subject', ''),
            email_data.get('sender', ''),
            email_data.get('recipient', ''),
            email_data.get('date'),
            email_data.get('snippet', ''),
            email_data.get('body', ''),
            email_data.get('category', ''),
            email_data.get('confidence'),
            email_data.get('important', False),
            email_data.get('unread', False),
            email_data.get('gmail_category', '')
        )
    
    result = execute_query(query, params)
    
    # Save keywords
    if result and isinstance(result, int) and result > 0 and 'keywords' in email_data and email_data['keywords']:
        # Delete existing keywords
        execute_query(
            "DELETE FROM keywords WHERE email_id = %s",
            (email_id,)
        )
        
        # Insert new keywords
        for keyword in email_data['keywords']:
            execute_query(
                "INSERT INTO keywords (email_id, keyword) VALUES (%s, %s)",
                (email_id, keyword)
            )
    
    return bool(result and isinstance(result, int) and result > 0)

def get_emails(user_id: Optional[int] = None, limit: int = 50, offset: int = 0, category: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get emails from the database.
    
    Args:
        user_id: User ID
        limit: Maximum number of emails to return
        offset: Offset for pagination
        category: Filter by category
        
    Returns:
        List of email dictionaries
    """
    query = "SELECT * FROM emails"
    params = []
    
    # Add filters
    conditions = []
    if user_id is not None:
        conditions.append("user_id = %s")
        params.append(user_id)
    
    if category is not None:
        conditions.append("category = %s")
        params.append(category)
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    
    # Add limit and offset
    query += " ORDER BY date DESC LIMIT %s OFFSET %s"
    params.extend([limit, offset])
    
    emails = execute_query(query, tuple(params), fetch=True)
    
    # Get keywords for each email
    if emails and isinstance(emails, list):
        for email in emails:
            keywords_result = execute_query(
                "SELECT keyword FROM keywords WHERE email_id = %s",
                (email['id'],),
                fetch=True
            )
            
            if keywords_result and isinstance(keywords_result, list):
                email['keywords'] = [k['keyword'] for k in keywords_result]
            else:
                email['keywords'] = []
    
    return emails if emails and isinstance(emails, list) else []

# Statistics operations
def update_statistics(user_id: Optional[int] = None) -> bool:
    """
    Update statistics for a user.
    
    Args:
        user_id: User ID
        
    Returns:
        True if successful, False if failed
    """
    # Get email counts
    params: Tuple = (user_id,) if user_id is not None else ()
    where_clause = "WHERE user_id = %s" if user_id is not None else ""
    
    total_query = f"SELECT COUNT(*) as count FROM emails {where_clause}"
    total = execute_query(total_query, params, fetch=True)
    
    # FIX: Corrected SQL syntax for the classified query
    if where_clause:
        classified_query = f"SELECT COUNT(*) as count FROM emails {where_clause} AND category IS NOT NULL"
        spam_query = f"SELECT COUNT(*) as count FROM emails {where_clause} AND category = 'spam'"
        hr_query = f"SELECT COUNT(*) as count FROM emails {where_clause} AND category = 'professional'"
        support_query = f"SELECT COUNT(*) as count FROM emails {where_clause} AND category = 'professional'"
        casual_query = f"SELECT COUNT(*) as count FROM emails {where_clause} AND category = 'casual'"
    else:
        classified_query = "SELECT COUNT(*) as count FROM emails WHERE category IS NOT NULL"
        spam_query = "SELECT COUNT(*) as count FROM emails WHERE category = 'spam'"
        hr_query = "SELECT COUNT(*) as count FROM emails WHERE category = 'professional'"
        support_query = "SELECT COUNT(*) as count FROM emails WHERE category = 'professional'"
        casual_query = "SELECT COUNT(*) as count FROM emails WHERE category = 'casual'"
    
    classified = execute_query(classified_query, params, fetch=True)
    spam = execute_query(spam_query, params, fetch=True)
    hr = execute_query(hr_query, params, fetch=True)
    support = execute_query(support_query, params, fetch=True)
    casual = execute_query(casual_query, params, fetch=True)
    
    # Calculate accuracy (placeholder - in a real app, you'd compare with user feedback)
    accuracy = 0.95
    
    # Helper function to safely extract count value
    def get_count(result: Optional[Union[List[Dict[str, Any]], int]]) -> int:
        if result and isinstance(result, list) and len(result) > 0 and 'count' in result[0]:
            return int(result[0]['count'])
        return 0
    
    # Check if statistics entry exists
    stats_query = "SELECT id FROM statistics WHERE user_id = %s" if user_id is not None else "SELECT id FROM statistics WHERE user_id IS NULL"
    stats = execute_query(stats_query, params, fetch=True)
    
    if stats and isinstance(stats, list) and len(stats) > 0:
        # Update existing statistics
        update_query = """
        UPDATE statistics SET
            total_emails = %s,
            classified_emails = %s,
            spam_emails = %s,
            hr_emails = %s,
            support_emails = %s,
            casual_emails = %s,
            accuracy = %s,
            last_updated = CURRENT_TIMESTAMP
        WHERE user_id = %s
        """
        update_params = (
            get_count(total),
            get_count(classified),
            get_count(spam),
            get_count(hr),
            get_count(support),
            get_count(casual),
            accuracy,
            user_id
        )
    else:
        # Insert new statistics
        update_query = """
        INSERT INTO statistics (
            user_id, total_emails, classified_emails, spam_emails,
            hr_emails, support_emails, casual_emails, accuracy
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        update_params = (
            user_id,
            get_count(total),
            get_count(classified),
            get_count(spam),
            get_count(hr),
            get_count(support),
            get_count(casual),
            accuracy
        )
    
    result = execute_query(update_query, update_params)
    
    return bool(result and isinstance(result, int) and result > 0)

def get_statistics(user_id: Optional[int] = None) -> Dict[str, Any]:
    """
    Get statistics for a user.
    
    Args:
        user_id: User ID
        
    Returns:
        Statistics dictionary
    """
    query = "SELECT * FROM statistics WHERE user_id = %s" if user_id is not None else "SELECT * FROM statistics WHERE user_id IS NULL"
    params = (user_id,) if user_id is not None else ()
    
    result = execute_query(query, params, fetch=True)
    
    if result and isinstance(result, list) and len(result) > 0:
        return result[0]
    
    # Return default statistics if none found
    return {
        'total_emails': 0,
        'classified_emails': 0,
        'spam_emails': 0,
        'hr_emails': 0,
        'support_emails': 0,
        'casual_emails': 0,
        'accuracy': 0
    }